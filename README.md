## pnpm

This project uses pnpm as the package manager. Please install pnpm before proceeding.

### Installing pnpm

#### Windows
```powershell
# Using npm (if you have Node.js installed)
npm install -g pnpm

# Using Chocolatey
choco install pnpm

# Using Scoop
scoop install pnpm

# Using winget
winget install pnpm
```

#### Linux
```bash
# Using npm (if you have Node.js installed)
npm install -g pnpm

# Using curl
curl -fsSL https://get.pnpm.io/install.sh | sh -

# Using wget
wget -qO- https://get.pnpm.io/install.sh | sh -

# On Ubuntu/Debian using apt
curl -fsSL https://get.pnpm.io/install.sh | sh -
source ~/.bashrc

# Using Homebrew (on Linux)
brew install pnpm
```

#### Using Corepack (Node.js 16.13+)
```bash
# Enable corepack (works on all platforms)
corepack enable
corepack use pnpm@latest
```

### Getting Started

1. Navigate to the project root directory
2. Install dependencies:
   ```bash
   pnpm install
   ```

### Useful pnpm Commands

- `pnpm install` - Install all dependencies
- `pnpm add <package>` - Add a dependency
- `pnpm add -D <package>` - Add a dev dependency
- `pnpm remove <package>` - Remove a dependency
- `pnpm run <script>` - Run a package script
- `pnpm audit` - Check for security vulnerabilities
- `pnpm update` - Update dependencies
- `pnpm dlx <package>` - Execute a package without installing it globally
