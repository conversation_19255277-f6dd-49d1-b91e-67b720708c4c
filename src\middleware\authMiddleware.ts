import { type Request, type Response, type NextFunction } from 'express'
import jwt, { type JwtPayload } from 'jsonwebtoken'
import { env } from '../utils/env'
import { type IUserData, Role } from '../models/User'

interface IRequest extends Request {
  user?: IUserData | JwtPayload
}

export const authorize = (permissions: string[]) => {
  return async (req: IRequest, res: Response, next: NextFunction) => {
    const user: IUserData | JwtPayload | undefined = req.user

    try {
      if (user !== undefined) {
        const userRoles = await Role.find({
          _id: { $in: (user.roles as any[]) || [] }
        })

        const hasPermission: boolean = permissions.every((permission) => {
          return userRoles.some((role) => role.name === permission)
        })

        if (!hasPermission) {
          res.forbidden()
          return
        }

        next()
      }
    } catch (err: any) {
      res.internalError(err.message)
    }
  }
}

export const isAuthenticated = (
  req: IRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    const token: string | undefined = req.headers.authorization?.split(' ')[1]

    const decoded = jwt.verify(token as string, env('JWT_SECRET_KEY'))

    req.user = decoded as IUserData
    next()
  } catch (err) {
    next(err)
  }
}
