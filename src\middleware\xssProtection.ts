import { type Request, type Response, type NextFunction } from 'express'

/**
 * XSS Protection Middleware
 * Replaces the deprecated xss-clean package with a custom implementation
 * using express-validator's escape functionality
 */
const xssProtection = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Helper function to recursively sanitize objects
  const sanitizeValue = (value: any): any => {
    if (typeof value === 'string') {
      // Basic XSS protection - remove script tags and dangerous attributes
      return value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
        .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
    }

    if (Array.isArray(value)) {
      return value.map(sanitizeValue)
    }

    if (value !== null && typeof value === 'object') {
      const sanitized: any = {}
      for (const key in value) {
        if (Object.prototype.hasOwnProperty.call(value, key)) {
          sanitized[key] = sanitizeValue(value[key])
        }
      }
      return sanitized
    }

    return value
  }

  // Sanitize request body
  if (req.body) {
    req.body = sanitizeValue(req.body)
  }

  // Sanitize query parameters
  if (req.query) {
    req.query = sanitizeValue(req.query)
  }

  // Sanitize URL parameters
  if (req.params) {
    req.params = sanitizeValue(req.params)
  }

  next()
}

export default xssProtection
