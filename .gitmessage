# Commit Message Template
# Format: [emoji] [type]: [message]
# Example: 📝 docs: update readme to reflect switch from yarn to pnpm

# Types & Emojis:
# 🐛 fix: bug fixes, hotfixes
# ✨ feat: new feature or enhancement
# 🛠️ refactor: code restructuring without behavior change
# 📚 docs: general documentation updates
# 📝 docs: minor doc edits, README changes
# 🎨 style: code style changes (formatting, linting)
# ⚡ perf: performance improvements
# ✅ test: adding or updating tests
# 🚚 chore: maintenance tasks, moving/renaming files
# 🔧 config: config file changes
# 📦 build: build-related changes
# 🔒 security: security fixes or improvements
# ♻️ cleanup: remove unused code, deps
# 🚀 deploy: deployment-related commits
# 🧪 ci: continuous integration changes
# 📊 analytics: tracking & analytics code
# 🗃️ db: database changes (schema/migrations)

# Message (write below this line):