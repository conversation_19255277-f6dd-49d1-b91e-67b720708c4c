import { type Application } from 'express'
import { env } from './env'

import authRoutes from '../routes/auth/authRoutes'
import userRoutes from '../routes/users/usersRoutes'

const apiVersion = env('API_VERSION', 'v1')
const apiUri = `/api/${apiVersion}`

const registerRoutes = (app: Application): void => {
  app.use(`${apiUri}/auth`, authRoutes)
  app.use(`${apiUri}/users`, userRoutes)
}

export default registerRoutes
