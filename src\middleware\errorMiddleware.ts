import { type Request, type Response, type NextFunction } from 'express'

import ResourceError from '../common/errors/ResourceError'

const errorMiddleware = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const _error = { ...err }

  _error.message = err.message

  // ID not found
  if (_error.kind === 'ObjectId') {
    next(new ResourceError(_error.kind, _error.value))
    return
  }

  // Duplicate error
  if (_error.code === 11000) {
    const objectRegEx = /dup key: {(.+?)}/
    const errorMessage = _error.message
    const match = errorMessage.match(objectRegEx)

    if (match !== undefined || match !== null) {
      const objectString: string = match[1].replace(/\\"/g, "'").trim()
      res.error(`The ${objectString} already exists`, 409)
      return
    }
    res.error(_error.message, 409)
    return
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    const message = Object.values(_error.errors).map((val: any) => {
      let msg: string = `The field '${val.path as string}' is `

      if (val.kind === 'regexp') {
        msg += 'invalid'
      } else {
        msg += val.kind as string
      }

      return msg
    })
    res.response({ errors: { message } }, 400)
    return
  }

  // Send error response
  res.error(
    _error.message,
    _error.statusCode !== undefined || _error.statusCode !== null
      ? _error.statusCode
      : 500
  )
}

export default errorMiddleware
