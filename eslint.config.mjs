import js from '@eslint/js'
import ts from 'typescript-eslint'

export default [
  // ignore non-source stuff
  {
    ignores: ['node_modules', 'dist', 'coverage', '.env', 'eslint.config.mjs']
  },

  js.configs.recommended,

  // base TS rules (no type-check)
  ...ts.configs.recommended,

  // enable type-checked rules ONLY for TS files
  ...ts.configs.recommendedTypeChecked,
  {
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.json'], // or switch to tsconfig.eslint.json below
        tsconfigRootDir: import.meta.dirname
      }
    }
  }
]
