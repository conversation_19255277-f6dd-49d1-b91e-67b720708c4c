import { type ResponseObject } from '../middleware/extendedResponse'

export const hasMessage = (data: any): data is ResponseObject => {
  return Object.prototype.hasOwnProperty.call(data, 'message')
}

export const isEmptyObject = (obj: object): boolean => {
  return Object.keys(obj).length === 0
}

export const validateEmail = (email: string): boolean => {
  const emailRegEx =
    /^(([^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*)|(".+"))@(([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"]{2,})$/

  return email.length > 3 && emailRegEx.test(email)
}
