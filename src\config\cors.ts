let origins: string[] | undefined

if (process.env.WHITELIST_URLS) {
  origins = process.env.WHITELIST_URLS.split(',')
}

const config = {
  origin(
    origin: string | undefined,
    cb: (err: Error | null, allow?: boolean) => void
  ) {
    if (
      origins == null ||
      origins.includes(origin ?? '') ||
      origin === undefined
    ) {
      cb(null, true)
    } else {
      cb(new Error('Not Allowed!'))
    }
  },
  optionsSuccessStatus: 200
}

export default config
