declare namespace Express {
  export interface Response {
    success(message?: any, statusCode?: number): void
    response(message?: any, statusCode?: number): void
    error(message?: any, statusCode?: number): void
    badRequest(message?: any, statusCode?: number): void
    forbidden(message?: any, statusCode?: number): void
    notFound(message?: any, statusCode?: number): void
    alreadyExists(message?: any, statusCode?: number): void
    unauthorized(message?: any, statusCode?: number): void
    internalError(message?: any, statusCode?: number): void
    data: any
  }
}
