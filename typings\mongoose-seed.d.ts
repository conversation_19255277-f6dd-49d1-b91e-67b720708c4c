declare module 'mongoose-seed' {
  export function connect(arg0: any, arg1: () => void)
  export function loadModels(
    arg0: {
      name: string // Specify the model name
      model: import('mongoose').Model<any, {}, {}, {}, any, any> // Pass the User model
    }[]
  )
  export function clearModels(arg0: string[], arg1: () => void)
  export function populateModels(
    data: {
      model: string // Specify the model name
      documents: {
        firstName: string
        lastName: string
        email: string
        password: import('mongoose').Document<
          unknown,
          {},
          import('../src/models/User').IPassword
        > &
          Omit<import('../src/models/User').IPassword, never> // Create a new Password instance
        roles: (import('mongoose').Document & Omit)[] // Create a new Role instance
      }[]
    }[],
    arg1: () => void
  )
  export function disconnect(): void
}
