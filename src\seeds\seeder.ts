import mongooseSeed from 'mongoose-seed'
import User, { Role, Password } from '../models/User' // Import the User model

// Define the data to be seeded
const data = [
  {
    model: 'User', // Specify the model name
    documents: [
      {
        firstName: '<PERSON>',
        lastName: '<PERSON><PERSON>',
        email: '<EMAIL>',
        password: new Password({ hash: 'password' }), // Create a new Password instance
        roles: [new Role({ name: 'user', permissions: ['read'] })] // Create a new Role instance
      }
      // Add more user data here if needed
    ]
  }
]

// Connect to MongoDB
mongooseSeed.connect(
  'mongodb+srv://dashawk:<EMAIL>/mrh?retryWrites=true&w=majority',
  function () {
    // Load the data into the User model
    mongooseSeed.loadModels([
      {
        name: 'User', // Specify the model name
        model: User // Pass the User model
      }
      // Add more models here if needed
    ])

    // Clear existing data and seed the new data
    mongooseSeed.clearModels(['User'], function () {
      mongooseSeed.populateModels(data, function () {
        mongooseSeed.disconnect() // Disconnect from MongoDB after seeding is done
      })
    })
  }
)
